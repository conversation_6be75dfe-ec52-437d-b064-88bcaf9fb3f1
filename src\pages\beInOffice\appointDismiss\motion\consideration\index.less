.tableTitle {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  letter-spacing: 0.3em;
  font-weight: bolder;
  font-family: 'Times New Roman', fzxbs;
  font-size: 33px;

  .desc {
    letter-spacing: 0.1em;
    font-family: 'Times New Roman', fzkt;
    font-size: 22px;
  }
}

.tableInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-family: 'Times New Roman', fzkt;
  font-size: 20px;
}

.tableHeadBox {
  width: 100%;
  position: relative;
  // overflow-y: scroll;
  // overflow-x: hidden;
}

.sftable {
  margin: 0 auto;
  font-family: 'Times New Roman';
  border: 2px solid;

  :global {
    .ant-select-selector {
      border: none !important;
    }

    .ant-select-selection-item {
      text-align: center;
    }

    .ant-select-arrow {
      display: none !important;
    }

    // 悬停显示删除按钮的样式
    .person-item-hover {
      &:hover .delete-btn-hover {
        opacity: 1 !important;
      }
    }

    // 悬停显示删除按钮的样式
    .person-item-hover {
      &:hover .delete-btn-hover {
        opacity: 1 !important;
      }
    }
  }

  thead {
    td {
      height: 60px;
      border-bottom: none !important;
      font-family: 'Times New Roman', fzht;
      font-size: 20px;
      font-weight: bold;
    }
  }

  tbody {
    td {
      border: 1px solid #000;
      height: 50px;

      & > div {
        font-family: 'Times New Roman', fzht;
        font-size: 19px;
      }

      :global {
        .ant-select,
        .ant-select-selector {
          height: 100% !important;
          align-items: center;
          padding: 0 !important;
        }

        .ant-select-selection-item {
          white-space: pre-wrap;
          padding-right: 5px !important;
        }
      }

      & > span:first-child {
        font-family: 'Times New Roman', fzfs;
        font-size: 20px;
        font-weight: bold;
        padding-top: 5px;
        display: inline-block;
      }

      p {
        text-indent: 2em;
        margin: 0;
        font-family: 'Times New Roman', fzkt;
        font-size: 18px;
      }

      textarea {
        border: none !important;
        padding: 2px;
        font-family: 'Times New Roman', fzfs;
        font-size: 18px;
      }
    }
  }

  .action {
    // display: none;
    width: 50px;
    height: 100%;
    padding: 1px 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: -51px;
    top: 0px;
    background-color: #ffffff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .tableRow {
    position: relative;
    height: 150px;

    .hover-icon {
      display: none;
    }

    .person-select:hover .hover-icon {
      display: block;
    }
  }

  // .tableRow:hover .action {
  //   width: 160px;
  //   padding: 10px;
  //   display: flex;
  //   position: absolute;
  //   right: -160px;
  //   top: 10px;
  //   background-color: #ffffff;
  //   border-radius: 4px;
  //   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  // }
}

.note {
  border: 2px dashed #000;
  font-weight: bolder;
  width: 252px;
  padding: 6px;
  font-family: 'Times New Roman', fzkt;
  font-size: 20px;
}

.meetingCommunicateTableRow {
  font-family: 'Times New Roman', fzfs;
  font-size: 20px;
  font-weight: bold;
  padding-top: 5px;
}

.fileAside {
  border-radius: 4px;
  padding: '16px 0px';
}

.dyContainer {
  width: 100%;
  display: flex;

  .dyRight {}

  .dyLeft {}
}
